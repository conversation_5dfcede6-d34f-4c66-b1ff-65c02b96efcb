{{- if .Values.networkPolicy.enabled -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "hacomms-audit-cleanup.fullname" . }}-netpol
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "hacomms-audit-cleanup.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "hacomms-audit-cleanup.selectorLabels" . | nindent 6 }}
  policyTypes:
  - Egress
  egress:
  {{- if .Values.networkPolicy.egress.allowDNS }}
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
  {{- end }}
  {{- if .Values.networkPolicy.egress.allowHTTPS }}
  # Allow HTTPS to SendGrid and Azure services
  - to: []
    ports:
    - protocol: TCP
      port: 443
  {{- end }}
  {{- if .Values.networkPolicy.egress.allowSQLServer }}
  # Allow SQL Server connection
  - to: []
    ports:
    - protocol: TCP
      port: 1433
  {{- end }}
{{- end }}
