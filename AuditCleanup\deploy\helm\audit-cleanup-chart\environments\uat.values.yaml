image:
  repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
  tag: latest
  pullPolicy: Always

global:
  namespace: axon-hacomms-uat

cronjob:
  enabled: true
  schedule: "0 2 1 * *"
  image:
    repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
    tag: uat
    pullPolicy: Always

  resources:
    requests:
      memory: "128Mi"
      cpu: "50m"
    limits:
      memory: "256Mi"
      cpu: "200m"

  rbac:
    enabled: true

app:
  retentionMonths: 12
  emailReceivers: "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>"
  environment: UAT
  logLevel: Information

keyVaultName: hac-uat-kv-eun
aspNetCoreEnvironment: UAT

databaseConnectionString: "#{database-connection-string-uat}#"
sendGridApiKey: "#{sendgrid-api-key-uat}#"

AzureAd:
  AppName: axon-hacomms-uat
  Instance: "https://login.microsoftonline.com/"
  TenantId: "66b904a2-2bfc-4d24-a410-96b77b32bf77"
  ClientId: "d95f8f43-d4c7-466b-8e19-49f357f2e833"
  Audience: "167cd45b-7d4f-4b3d-8c05-a87f12c40609"
  TokenExpiration: '86400' # Token is valid for 1 day
  AllowWebApiToBeAuthorizedByACL: true
  Scope: "api://smartphlex-prodeu/.default"

database:
  secretName: hacomms-uat-secrets
  secretKey: ConnectionStrings--default

sendgrid:
  secretName: sendgrid-secret
  secretKey: ConnectionStrings--SendGrid

secrets:
  enabled: true
