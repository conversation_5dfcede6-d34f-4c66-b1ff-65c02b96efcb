# HAComms Scheduled Jobs

This repository contains scheduled job services for the HAComms system, including audit data cleanup and maintenance tasks.

## Projects

### Audit Cleanup Service

A lightweight console application that performs automated cleanup of old audit data from the database. The service connects directly to SQL Server using ADO.NET for optimal performance and minimal dependencies.

**Key Features:**
- Configurable retention period (default: 3 months)
- Direct SQL Server connectivity using ADO.NET (no Entity Framework overhead)
- Email notifications via SendGrid
- Azure Key Vault integration for secure configuration
- Comprehensive logging and error handling
- Docker support for containerized deployment

## Architecture

The Audit Cleanup Service follows a clean architecture pattern:

- **Services**: Business logic and orchestration
- **Repositories**: Data access layer using ADO.NET
- **Models**: Data transfer objects and result models
- **Interfaces**: Contracts for dependency injection

### Technology Stack

- **.NET 8.0**: Target framework
- **ADO.NET**: Direct database access (replaced Entity Framework for better performance)
- **Microsoft.Data.SqlClient**: SQL Server connectivity
- **SendGrid**: Email notifications
- **Azure Key Vault**: Secure configuration management
- **Docker**: Containerization support

## Getting Started

### Prerequisites

- .NET 8.0 SDK
- SQL Server (for audit data storage)
- SendGrid account (for email notifications)
- Azure Key Vault (for production configuration)

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Axon.HAComms.ScheduledJobs
   ```

2. **Configure connection strings**
   Update `appsettings.Development.json` in the AuditCleanup service:
   ```json
   {
     "ConnectionStrings": {
       "default": "Server=.;Database=YourDatabase;Integrated Security=true;TrustServerCertificate=true;",
       "SendGrid": "your-sendgrid-api-key"
     },
     "AuditCleanup": {
       "RetentionMonths": 12,
       "EmailReceivers": "<EMAIL>"
     }
   }
   ```

3. **Build the solution**
   ```bash
   dotnet build
   ```

4. **Run tests**
   ```bash
   dotnet test
   ```

### Production Deployment

The service is designed to run as a scheduled job (e.g., via cron or Azure Container Instances).

## Build and Test

### Building the Solution
```bash
# Build all projects
dotnet build

# Build specific project
dotnet build AuditCleanup/src/Axon.HAComms.AuditCleanupService/
```

### Running Tests
```bash
# Run all tests
dotnet test

# Run specific test project
dotnet test AuditCleanup/test/Axon.HAComms.AuditCleanupService.Tests/
```

### Docker Build
```bash
cd AuditCleanup/src/Axon.HAComms.AuditCleanupService/
docker build -t hacomms-audit-cleanup .
```

## Configuration

### Audit Cleanup Settings

| Setting | Description | Default | Required |
|---------|-------------|---------|----------|
| `AuditCleanup:RetentionMonths` | Number of months to retain audit data | 3 | No |
| `AuditCleanup:EmailReceivers` | Semicolon-separated email addresses for notifications | - | Yes |
| `ConnectionStrings:default` | SQL Server connection string | - | Yes |
| `ConnectionStrings:SendGrid` | SendGrid API key | - | Yes |

### Azure Configuration

For production deployments, sensitive configuration is stored in Azure Key Vault:
- `SendGrid`: SendGrid API key
- Connection strings and other sensitive data

## License

This project is proprietary software. All rights reserved.