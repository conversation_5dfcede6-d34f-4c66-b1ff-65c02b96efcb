using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Configuration;

namespace Axon.HAComms.AuditCleanupService.Extensions;

public static class ConfigurationBuilderExtensions
{
    /// <summary>
    /// Adds Azure Key Vault configuration if KeyVaultName is configured
    /// </summary>
    public static IConfigurationBuilder AddKeyVaultIfConfigured(this IConfigurationBuilder builder)
    {
        var tempConfig = builder.Build();
        var keyVaultName = tempConfig["KeyVaultName"];

        if (!string.IsNullOrEmpty(keyVaultName))
        {
            var keyVaultUri = new Uri($"https://{keyVaultName}.vault.azure.net/");
            var credential = new DefaultAzureCredential();
            var client = new SecretClient(keyVaultUri, credential);

            builder.AddAzureKeyVault(client, new KeyVaultSecretManager());
        }

        return builder;
    }
}
