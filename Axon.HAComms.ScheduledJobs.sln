Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{E1B2C3D4-5F6A-7B8C-9D0E-1F2A3B4C5D6E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{F2C3D4E5-6A7B-8C9D-0E1F-2A3B4C5D6E7F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AuditCleanup", "AuditCleanup", "{95F2B23B-ED74-436A-8910-48C1F6779091}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.AuditCleanupService", "AuditCleanup\src\Axon.HAComms.AuditCleanupService\Axon.HAComms.AuditCleanupService.csproj", "{9B9B4DD8-BFBE-4EEB-9C3F-0074E297BF50}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.HAComms.AuditCleanupService.Tests", "AuditCleanup\test\Axon.HAComms.AuditCleanupService.Tests\Axon.HAComms.AuditCleanupService.Tests.csproj", "{76AEEAB1-0572-4A3D-BA16-C0EF9E321401}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9B9B4DD8-BFBE-4EEB-9C3F-0074E297BF50}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B9B4DD8-BFBE-4EEB-9C3F-0074E297BF50}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B9B4DD8-BFBE-4EEB-9C3F-0074E297BF50}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B9B4DD8-BFBE-4EEB-9C3F-0074E297BF50}.Release|Any CPU.Build.0 = Release|Any CPU
		{76AEEAB1-0572-4A3D-BA16-C0EF9E321401}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76AEEAB1-0572-4A3D-BA16-C0EF9E321401}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76AEEAB1-0572-4A3D-BA16-C0EF9E321401}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{76AEEAB1-0572-4A3D-BA16-C0EF9E321401}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E1B2C3D4-5F6A-7B8C-9D0E-1F2A3B4C5D6E} = {95F2B23B-ED74-436A-8910-48C1F6779091}
		{F2C3D4E5-6A7B-8C9D-0E1F-2A3B4C5D6E7F} = {95F2B23B-ED74-436A-8910-48C1F6779091}
		{9B9B4DD8-BFBE-4EEB-9C3F-0074E297BF50} = {E1B2C3D4-5F6A-7B8C-9D0E-1F2A3B4C5D6E}
		{76AEEAB1-0572-4A3D-BA16-C0EF9E321401} = {F2C3D4E5-6A7B-8C9D-0E1F-2A3B4C5D6E7F}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C3D4E5F6-A7B8-9C0D-1E2F-3A4B5C6D7E8F}
	EndGlobalSection
EndGlobal
