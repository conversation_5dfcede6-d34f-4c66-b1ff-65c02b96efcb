{{- if .Values.secrets.enabled -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.database.secretName }}
  annotations:
    checkov.io/skip1: CKV_K8S_21=The namespace is being defined as helm argument
stringData:
  {{ .Values.database.secretKey }}: '{{ .Values.databaseConnectionString }}'
---
apiVersion: v1
kind: Secret
metadata:
  name: sendgrid-secret
  annotations:
    checkov.io/skip1: CKV_K8S_21=The namespace is being defined as helm argument
stringData:
  {{ .Values.sendgrid.secretKey }}: '{{ .Values.sendGridApiKey }}'
{{- end }}
