using Axon.HAComms.AuditCleanupService.Interfaces;
using Axon.HAComms.AuditCleanupService.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Axon.HAComms.AuditCleanupService.Services;

public class EmailNotificationService(
    IEmailService emailService,
    IConfiguration configuration,
    ILogger<EmailNotificationService> logger) : IEmailNotificationService
{
    public async Task SendCleanupNotificationAsync(AuditCleanupResult result, CancellationToken cancellationToken = default)
    {
        var recipients = GetEmailRecipients();
        if (recipients is null || recipients.Length is 0)
        {
            logger.LogWarning("No email recipients configured for cleanup service");
            return;
        }

        var subject = result.Success
            ? "HAComms Audit Cleanup - Success"
            : "HAComms Audit Cleanup - Failed";

        var body = BuildCleanupNotificationBody(result);

        try
        {
            var (success, statusCode) = await emailService.Send(recipients, subject, body);
            
            if (success)
            {
                logger.LogInformation("Cleanup notification email sent successfully to {Recipients}", string.Join(", ", recipients));
            }
            else
            {
                logger.LogWarning("Failed to send cleanup notification email. Status code: {StatusCode}", statusCode);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error sending cleanup notification email");
        }
    }

    private string[]? GetEmailRecipients()
    {
        var recipients = configuration.GetValue<string>("AuditCleanup:EmailReceivers", "");
        return recipients?.Split(';', StringSplitOptions.RemoveEmptyEntries)
                        .Select(r => r.Trim())
                        .Where(r => !string.IsNullOrEmpty(r))
                        .ToArray();
    }

    private static string BuildCleanupNotificationBody(AuditCleanupResult result)
    {
        var sb = new StringBuilder();
        
        sb.AppendLine("HAComms Audit Data Cleanup Report");
        sb.AppendLine("=====================================");
        sb.AppendLine();
        
        sb.AppendLine($"Execution Time: {result.ExecutionTime:yyyy-MM-dd HH:mm:ss} UTC");
        sb.AppendLine($"Status: {(result.Success ? "SUCCESS" : "FAILED")}");
        sb.AppendLine($"Duration: {result.Duration.TotalSeconds:F2} seconds");
        sb.AppendLine($"Retention Period: {result.RetentionMonths} months");
        sb.AppendLine($"Threshold Date: {result.ThresholdDate:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine();

        if (result.Success)
        {
            sb.AppendLine($"Records Deleted: {result.RecordsDeleted:N0}");
            sb.AppendLine();
            sb.AppendLine("The audit data cleanup completed successfully.");
            
            if (result.RecordsDeleted == 0)
            {
                sb.AppendLine("No records were found that exceeded the retention period.");
            }
            else
            {
                sb.AppendLine($"Successfully removed {result.RecordsDeleted:N0} audit records older than {result.ThresholdDate:yyyy-MM-dd}.");
            }
        }
        else
        {
            sb.AppendLine("ERROR DETAILS:");
            sb.AppendLine($"Error Message: {result.ErrorMessage}");
            sb.AppendLine();
            sb.AppendLine("The audit data cleanup failed. Please check the application logs for more details.");
        }

        sb.AppendLine();
        sb.AppendLine("---");
        sb.AppendLine("This is an automated message from the HAComms Audit Cleanup Service.");

        return sb.ToString();
    }
}
