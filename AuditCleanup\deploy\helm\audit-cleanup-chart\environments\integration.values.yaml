image:
  repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
  tag: int
  pullPolicy: Always

global:
  namespace: axon-hacomms-int

cronjob:
  enabled: true
  schedule: "0 2 1 * *"
  image:
    repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
    tag: int
    pullPolicy: Always

  resources:
    requests:
      memory: "128Mi"
      cpu: "50m"
    limits:
      memory: "256Mi"
      cpu: "200m"

  rbac:
    enabled: true

app:
  retentionMonths: 3
  emailReceivers: "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>"
  environment: Integration
  logLevel: Information

keyVaultName: hac-int-kv-eun
aspNetCoreEnvironment: Integration

databaseConnectionString: "#{database-connection-string-int}#"
sendGridApiKey: "#{sendgrid-api-key-int}#"

AzureAd:
  AppName: axon-hacomms-int
  Instance: "https://login.microsoftonline.com/"
  TenantId: "66b904a2-2bfc-4d24-a410-96b77b32bf77"
  ClientId: "6cc7e19d-3d23-455b-a2a3-f2a2e8af6d7c"
  Audience: "167cd45b-7d4f-4b3d-8c05-a87f12c40609"
  TokenExpiration: '86400' # Token is valid for 1 day
  AllowWebApiToBeAuthorizedByACL: true
  Scope: "api://smartphlex-int/.default"

database:
  secretName: hacomms-int-secrets
  secretKey: ConnectionStrings--default

sendgrid:
  secretName: sendgrid-secret
  secretKey: ConnectionStrings--SendGrid

secrets:
  enabled: true
