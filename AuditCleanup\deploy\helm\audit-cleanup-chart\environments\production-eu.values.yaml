image:
  repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
  tag: latest
  pullPolicy: Always

global:
  namespace: axon-hacomms-prod

cronjob:
  enabled: true
  schedule: "0 2 1 * *"
  image:
    repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
    tag: prod
    pullPolicy: Always

  resources:
    requests:
      memory: "128Mi"
      cpu: "50m"
    limits:
      memory: "256Mi"
      cpu: "200m"

  rbac:
    enabled: true

app:
  retentionMonths: 12
  emailReceivers: "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>"
  environment: Production
  logLevel: Information

keyVaultName: hac-prod-kv-eun
aspNetCoreEnvironment: Production

databaseConnectionString: "#{database-connection-string-prod}#"
sendGridApiKey: "#{sendgrid-api-key-prod}#"

AzureAd:
  AppName: axon-hacomms-prod
  Instance: "https://login.microsoftonline.com/"
  TenantId: "66b904a2-2bfc-4d24-a410-96b77b32bf77"
  ClientId: "a2a1c66b-ebcf-4aee-93a6-c3e8d4552792"
  Audience: "167cd45b-7d4f-4b3d-8c05-a87f12c40609"
  TokenExpiration: '86400' # Token is valid for 1 day
  AllowWebApiToBeAuthorizedByACL: true
  Scope: "api://smartphlex-prodeu/.default"

database:
  secretName: hacomms-prod-secrets
  secretKey: ConnectionStrings--default

sendgrid:
  secretName: sendgrid-secret
  secretKey: ConnectionStrings--SendGrid

secrets:
  enabled: true
