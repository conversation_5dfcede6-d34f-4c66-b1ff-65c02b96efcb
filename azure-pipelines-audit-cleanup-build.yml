name: "$(Date:yyyyMMdd).$(Rev:r)"
parameters:
  - name: Analyse_Packages
    displayName: Analyse Packages
    type: boolean
    default: false
  - name: Push_Docker_Image
    displayName: Push Docker Image
    type: boolean
    default: false

pool:
  vmImage: 'ubuntu-latest'

trigger:
  - develop
  - main
  - release/*
  - hotfix/*

variables:
  - name: Source_Branch
    value: $[replace(variables['Build.SourceBranch'],'refs/heads/','')]
  - name: Push_Docker_Image
    value: $[or(in(variables['Source_Branch'], 'main','develop'), startsWith(variables['Source_Branch'], 'release/'), eq('${{ parameters.Push_Docker_Image }}', true))]
  - name: Long_Lived_Branch
    value: $[or(in(variables['Source_Branch'], 'main','master','develop'), startsWith(variables['Source_Branch'], 'release/'))]
  - name: Version_Number
    value: "1.0.0"
  - name: Build_Number
    value: $[counter(variables['Version_Number'], 0)]
  - name: Build_Configuration
    value: "Release"
  - group: Nuget
  - name: NuGet_Source
    value: $[variables.Source]

resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git

stages:
  - stage: Prerequisites
    jobs:
      - job: CalculateVersion
        displayName: "Calculate Version"
        steps:
          - template: General/calculate-version.yml@templates
            parameters:
              VersionNumber: "$(Version_Number)"
              BuildNumber: "$(Build_Number)"
              BranchName: "$(Source_Branch)"
          - template: WhiteSource/analyse.yml@templates
            parameters:
              SolutionName: 'Axon.HAComms.ScheduledJobs.sln'
              LongLivedBranch: "variables['Long_Lived_Branch']"
              WhiteSourceProductName: "Axon_HAComms_AuditCleanup_$(Build.SourceBranchName)"

  - template: Build/dotnet/dkr-build-test-analyse-push.yml@templates
    parameters:
      NugetSource: "$(NuGet_Source)"
      BuildConfiguration: "$(Build_Configuration)"
      SourceBranch: "variables['Source_Branch']"
      VersionNumber: "$(Version_Number)"
      SolutionName: "AuditCleanup/src/Axon.HAComms.AuditCleanupService/Axon.HAComms.AuditCleanupService.csproj"
      DockerRepository: "axon-hacomms-audit-cleanup"
      DockerRepositoryTest: "axon-hacomms-audit-cleanup-tests"
      DockerTestContainer: "axon-hacomms-scheduledjobs_axon-hacomms-audit-cleanup-tests_1"
      SonarProjectKey: "Phlexglobal_Axon.HAComms.ScheduledJobs"
      SonarProjectName: "Axon.HAComms.ScheduledJobs"
      PrerequisiteStage: Prerequisites
      AnalysePackages: "${{ parameters.Analyse_Packages }}"
      PushDockerImage: "variables['Push_Docker_Image']"