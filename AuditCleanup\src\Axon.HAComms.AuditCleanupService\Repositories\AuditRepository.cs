using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.AuditCleanupService.Repositories;

public class AuditRepository(IConfiguration configuration, ILogger<AuditRepository> logger) : IAuditRepository
{
    private readonly string _connectionString = configuration.GetConnectionString("default")
        ?? throw new InvalidOperationException("Default connection string is not configured.");

    private const int BatchSize = 1000;
    private const int CommandTimeoutSeconds = 30;
    private const int BatchDelayMilliseconds = 100;

    public async Task<int> DeleteAuditRecordsOlderThanAsync(DateTime thresholdDate, CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync(cancellationToken);

            var totalRecords = await GetTotalRecordsToDeleteAsync(connection, thresholdDate, cancellationToken);

            if (totalRecords == 0)
            {
                logger.LogInformation("No audit records found older than {ThresholdDate}", thresholdDate);
                return 0;
            }

            logger.LogInformation("Found {TotalRecords} audit records to delete older than {ThresholdDate}",
                totalRecords, thresholdDate);

            var totalDeleted = await DeleteRecordsInBatchesAsync(connection, thresholdDate, totalRecords, cancellationToken);

            logger.LogInformation("Successfully deleted {TotalDeleted} audit records older than {ThresholdDate}",
                totalDeleted, thresholdDate);

            return totalDeleted;
        }
        catch (SqlException ex)
        {
            logger.LogError(ex, "SQL error occurred while deleting audit records older than {ThresholdDate}", thresholdDate);
            throw new InvalidOperationException($"Failed to delete audit records older than {thresholdDate:yyyy-MM-dd} due to database error.", ex);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error occurred while deleting audit records older than {ThresholdDate}", thresholdDate);
            throw new InvalidOperationException($"Failed to delete audit records older than {thresholdDate:yyyy-MM-dd} due to an unexpected error.", ex);
        }
    }

    private async Task<int> GetTotalRecordsToDeleteAsync(SqlConnection connection, DateTime thresholdDate, CancellationToken cancellationToken)
    {
        const string countSql = "SELECT COUNT(*) FROM [dbo].[Audit] WHERE StartDate <= @ThresholdDate";

        using var countCommand = new SqlCommand(countSql, connection);
        countCommand.CommandTimeout = CommandTimeoutSeconds;
        countCommand.Parameters.Add(CreateDateTimeParameter("@ThresholdDate", thresholdDate));

        return (int)await countCommand.ExecuteScalarAsync(cancellationToken);
    }

    private async Task<int> DeleteRecordsInBatchesAsync(SqlConnection connection, DateTime thresholdDate, int totalRecords, CancellationToken cancellationToken)
    {
        const string deleteSql = "DELETE TOP(@BatchSize) FROM [dbo].[Audit] WHERE StartDate <= @ThresholdDate";

        var totalDeleted = 0;
        int batchDeleted;

        do
        {
            batchDeleted = await DeleteSingleBatchAsync(connection, deleteSql, thresholdDate, cancellationToken);
            totalDeleted += batchDeleted;

            if (batchDeleted > 0)
            {
                logger.LogInformation("Deleted batch of {BatchDeleted} records. Total deleted: {TotalDeleted}/{TotalRecords}",
                    batchDeleted, totalDeleted, totalRecords);

                await Task.Delay(BatchDelayMilliseconds, cancellationToken);
            }

        } while (batchDeleted > 0);

        return totalDeleted;
    }

    private async Task<int> DeleteSingleBatchAsync(SqlConnection connection, string deleteSql, DateTime thresholdDate, CancellationToken cancellationToken)
    {
        using var deleteCommand = new SqlCommand(deleteSql, connection);
        deleteCommand.CommandTimeout = CommandTimeoutSeconds;
        deleteCommand.Parameters.Add(CreateIntParameter("@BatchSize", BatchSize));
        deleteCommand.Parameters.Add(CreateDateTimeParameter("@ThresholdDate", thresholdDate));

        return await deleteCommand.ExecuteNonQueryAsync(cancellationToken);
    }

    private static SqlParameter CreateDateTimeParameter(string parameterName, DateTime value) =>
        new(parameterName, System.Data.SqlDbType.DateTime2) { Value = value };

    private static SqlParameter CreateIntParameter(string parameterName, int value) =>
        new(parameterName, System.Data.SqlDbType.Int) { Value = value };
}
