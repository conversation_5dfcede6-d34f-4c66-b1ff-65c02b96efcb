services:
  axon-hacomms-audit-cleanup:
    image: axon-hacomms-audit-cleanup:${VERSION_NO}
    build:
      context: .
      dockerfile: "AuditCleanup/src/Axon.HAComms.AuditCleanupService/Dockerfile"
      target: "final"
      args:
        versionNo: "${VERSION_NO}"
        nugetSource: ${NUGET_SOURCE}
        nugetPassword: ${NUGET_PASSWORD}
        projectName: "Axon.HAComms"
        configuration: "Release"
    networks:
      - overlay

  axon-hacomms-audit-cleanup-tests:
    image: axon-hacomms-audit-cleanup-tests:${VERSION_NO}
    build:
      context: .
      dockerfile: "AuditCleanup/src/Axon.HAComms.AuditCleanupService/Dockerfile"
      target: "testrunner"
      args:
        versionNo: "${VERSION_NO}"
        nugetSource: ${NUGET_SOURCE}
        nugetPassword: ${NUGET_PASSWORD}
    networks:
      - overlay

networks:
  overlay:
