using Axon.HAComms.AuditCleanupService.Extensions;
using Axon.HAComms.AuditCleanupService.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace Axon.HAComms.AuditCleanupService;

internal sealed class Program
{
    private Program() { }

    private static async Task<int> Main(string[] args)
    {
        // Set culture to handle globalization properly
        CultureInfo.DefaultThreadCurrentCulture = CultureInfo.InvariantCulture;
        CultureInfo.DefaultThreadCurrentUICulture = CultureInfo.InvariantCulture;

        var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";

        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{environmentName}.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables()
            .AddKeyVaultIfConfigured()
            .Build();

        await using var serviceProvider = new ServiceCollection()
            .AddSingleton<IConfiguration>(configuration)
            .AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddConsole();
                builder.AddConfiguration(configuration.GetSection("Logging"));
            })
            .AddSendGridServices(configuration)
            .AddAuditCleanupServices()
            .BuildServiceProvider();

        // Get required services
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var auditCleanupService = serviceProvider.GetRequiredService<IAuditCleanupService>();
        var emailNotificationService = serviceProvider.GetRequiredService<IEmailNotificationService>();

        try
        {
            logger.LogInformation("Starting audit cleanup process...");

            var result = await auditCleanupService.ExecuteCleanupAsync();

            await emailNotificationService.SendCleanupNotificationAsync(result);

            var exitCode = result.Success ? 0 : 1;

            if (result.Success)
            {
                logger.LogInformation("Audit cleanup completed successfully. Deleted {RecordsDeleted} records in {Duration}ms",
                    result.RecordsDeleted, result.Duration.TotalMilliseconds);
            }
            else
            {
                logger.LogError("Audit cleanup failed: {ErrorMessage}", result.ErrorMessage);
            }

            return exitCode;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during audit cleanup");
            return 1;
        }
    }
}
