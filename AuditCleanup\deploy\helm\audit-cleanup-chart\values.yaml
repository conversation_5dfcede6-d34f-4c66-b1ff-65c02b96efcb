global:
  namespace: axon-hacomms

cronjob:
  schedule: "0 * * * 2"
  enabled: true
  concurrencyPolicy: Forbid
  suspend: false
  backoffLimit: 3
  activeDeadlineSeconds: 3600
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1

  image:
    repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
    tag: latest
    pullPolicy: Always

  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "500m"

  rbac:
    enabled: true

pod:
  annotations: {}

svcAccount:
  enabled: false

app:
  name: hacomms-audit-cleanup
  component: maintenance
  retentionMonths: 12
  emailReceivers: "<EMAIL>"
  environment: Production
  logLevel: Information

keyVaultName: your-keyvault-name
aspNetCoreEnvironment: Production

databaseConnectionString: "#{database-connection-string}#"
sendGridApiKey: "#{sendgrid-api-key}#"

AzureAd:
  Instance: "https://login.microsoftonline.com/"
  TenantId: "66b904a2-2bfc-4d24-a410-96b77b32bf77"
  ClientId: "bb71e917-9ba3-4867-b2a1-af346630efad"
  Audience: "167cd45b-7d4f-4b3d-8c05-a87f12c40609"
  AllowWebApiToBeAuthorizedByACL: true
  Scope: "api://smartphlex-dev/.default"

database:
  secretName: hacomms-secrets
  secretKey: ConnectionStrings--default

sendgrid:
  secretName: sendgrid-secret
  secretKey: ConnectionStrings--SendGrid

secrets:
  enabled: false

securityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 64198
  runAsGroup: 64198
  capabilities:
    drop:
      - ALL

podSecurityContext:
  runAsNonRoot: true
  runAsUser: 64198
  runAsGroup: 64198
  fsGroup: 64198
  seccompProfile:
    type: RuntimeDefault

nodeSelector:
  kubernetes.io/os: linux

tolerations:
  - key: "maintenance"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

affinity: {}
pod:
  annotations:
    checkov.io/skip1: CKV_K8S_21=The namespace is being defined as helm argument
    checkov.io/skip2: CKV_K8S_14=The tag is being defined as helm argument
    checkov.io/skip3: CKV_K8S_43=We cannot use digest since these are regenerated on every image

networkPolicy:
  enabled: true
  egress:
    allowDNS: true
    allowHTTPS: true
    allowSQLServer: true
