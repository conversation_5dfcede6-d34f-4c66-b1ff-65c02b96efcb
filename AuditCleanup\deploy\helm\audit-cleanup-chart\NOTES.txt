1. The HAComms Audit Cleanup CronJob has been deployed successfully!

2. CronJob Details:
   - Name: {{ include "hacomms-audit-cleanup.fullname" . }}-cronjob
   - Namespace: {{ .Values.global.namespace }}
   - Schedule: {{ .Values.cronjob.schedule }}
   - Next run: Check with `kubectl get cronjob {{ include "hacomms-audit-cleanup.fullname" . }}-cronjob -n {{ .Values.global.namespace }}`

3. To view the CronJob status:
   kubectl get cronjob {{ include "hacomms-audit-cleanup.fullname" . }}-cronjob -n {{ .Values.global.namespace }}

4. To view job history:
   kubectl get jobs -l app.kubernetes.io/name={{ include "hacomms-audit-cleanup.name" . }} -n {{ .Values.global.namespace }}

5. To view logs from the latest job:
   kubectl logs -l app.kubernetes.io/name={{ include "hacomms-audit-cleanup.name" . }} -n {{ .Values.global.namespace }} --tail=100

6. To manually trigger a job:
   kubectl create job --from=cronjob/{{ include "hacomms-audit-cleanup.fullname" . }}-cronjob manual-cleanup-$(date +%s) -n {{ .Values.global.namespace }}

7. Configuration:
   - Retention Period: {{ .Values.app.retentionMonths }} months
   - Email Recipients: {{ .Values.app.emailReceivers }}
   - Key Vault: https://{{ .Values.keyVaultName }}.vault.azure.net/

{{- if .Values.secrets.enabled }}
8. Secrets Created:
   - Secret Name: {{ .Values.database.secretName }}
   - Contains: Database connection string and SendGrid API key
   - Note: For production, use external secret management (Azure Key Vault, etc.)
{{- else }}
8. External Secrets Required:
   - Create secret: {{ .Values.database.secretName }}
   - Required keys: {{ .Values.database.secretKey }}, {{ .Values.sendgrid.secretKey }}
   - Use Azure Key Vault or other external secret management
{{- end }}

9. Important Notes:
   - Ensure the Azure AD application has access to the Key Vault
   - Verify the SendGrid API key is available as '{{ .Values.sendgrid.secretKey }}'
   - Check that the database connection secret exists in the namespace
