using Axon.HAComms.AuditCleanupService.Interfaces;
using Axon.HAComms.AuditCleanupService.Models;
using Axon.HAComms.AuditCleanupService.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Axon.HAComms.AuditCleanupService.Services;

public class AuditCleanupService(
    IAuditRepository auditRepository,
    IConfiguration configuration,
    ILogger<AuditCleanupService> logger) : IAuditCleanupService
{
    public async Task<AuditCleanupResult> ExecuteCleanupAsync(CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var retentionMonths = configuration.GetValue<int>("AuditCleanup:RetentionMonths", 3);
        var thresholdDate = DateTime.UtcNow.AddMonths(-retentionMonths);

        logger.LogInformation("Starting audit data cleanup. Retention: {RetentionMonths} months, Threshold: {ThresholdDate}",
            retentionMonths, thresholdDate);

        try
        {
            var deletedRows = await auditRepository.DeleteAuditRecordsOlderThanAsync(thresholdDate, cancellationToken);

            var endTime = DateTime.UtcNow;
            var duration = endTime - startTime;

            logger.LogInformation("Audit cleanup completed successfully. Deleted {DeletedRows} records in {Duration}ms",
                deletedRows, duration.TotalMilliseconds);

            return new AuditCleanupResult
            {
                Success = true,
                RecordsDeleted = deletedRows,
                ExecutionTime = startTime,
                Duration = duration,
                RetentionMonths = retentionMonths,
                ThresholdDate = thresholdDate
            };
        }
        catch (Exception ex)
        {
            var endTime = DateTime.UtcNow;
            var duration = endTime - startTime;

            logger.LogError(ex, "Error occurred during audit cleanup");

            return new AuditCleanupResult
            {
                Success = false,
                RecordsDeleted = 0,
                ExecutionTime = startTime,
                Duration = duration,
                ErrorMessage = ex.Message,
                Exception = ex,
                RetentionMonths = retentionMonths,
                ThresholdDate = thresholdDate
            };
        }
    }
}
