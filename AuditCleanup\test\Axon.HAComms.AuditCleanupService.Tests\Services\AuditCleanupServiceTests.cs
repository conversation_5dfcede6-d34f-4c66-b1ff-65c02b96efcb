using Axon.HAComms.AuditCleanupService.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Xunit;
using AuditCleanupServiceClass = Axon.HAComms.AuditCleanupService.Services.AuditCleanupService;

namespace Axon.HAComms.AuditCleanupService.Tests.Services;

public class AuditCleanupServiceTests
{
    private readonly AuditCleanupServiceClass sut;
    private readonly IAuditRepository mockAuditRepository;

    public AuditCleanupServiceTests()
    {
        var mockLogger = Substitute.For<ILogger<AuditCleanupServiceClass>>();
        mockAuditRepository = Substitute.For<IAuditRepository>();

        // Create a real configuration object with test data
        var configData = new Dictionary<string, string?>
        {
            {"AuditCleanup:RetentionMonths", "12"}
        };

        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();

        sut = new AuditCleanupServiceClass(mockAuditRepository, configuration, mockLogger);
    }

    [Fact]
    public async Task ExecuteCleanupAsync_ValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var expectedDeletedRows = 20;
        mockAuditRepository.DeleteAuditRecordsOlderThanAsync(Arg.Any<DateTime>(), Arg.Any<CancellationToken>())
            .Returns(expectedDeletedRows);

        // Act
        var result = await sut.ExecuteCleanupAsync();

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.RecordsDeleted.ShouldBe(expectedDeletedRows);
        result.RetentionMonths.ShouldBe(12);
        result.ErrorMessage.ShouldBeNull();
        result.Exception.ShouldBeNull();

        // Verify repository was called
        await mockAuditRepository.Received(1).DeleteAuditRecordsOlderThanAsync(
            Arg.Any<DateTime>(),
            Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ExecuteCleanupAsync_RepositoryThrowsException_ReturnsFailureResult()
    {
        // Arrange
        var expectedException = new InvalidOperationException("Database connection failed");
        mockAuditRepository.When(x => x.DeleteAuditRecordsOlderThanAsync(Arg.Any<DateTime>(), Arg.Any<CancellationToken>()))
            .Do(x => throw expectedException);

        // Act
        var result = await sut.ExecuteCleanupAsync();

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeFalse();
        result.RecordsDeleted.ShouldBe(0);
        result.RetentionMonths.ShouldBe(12);
        result.ErrorMessage.ShouldBe(expectedException.Message);
        result.Exception.ShouldBe(expectedException);
    }

    [Fact]
    public async Task ExecuteCleanupAsync_DefaultRetentionMonths_Uses3Months()
    {
        // Arrange
        var configData = new Dictionary<string, string?>(); // No retention months configured
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();

        var mockLogger = Substitute.For<ILogger<AuditCleanupServiceClass>>();
        var testSut = new AuditCleanupServiceClass(mockAuditRepository, configuration, mockLogger);

        mockAuditRepository.DeleteAuditRecordsOlderThanAsync(Arg.Any<DateTime>(), Arg.Any<CancellationToken>())
            .Returns(10);

        // Act
        var result = await testSut.ExecuteCleanupAsync();

        // Assert
        result.ShouldNotBeNull();
        result.Success.ShouldBeTrue();
        result.RetentionMonths.ShouldBe(3); // Default value
    }
}
