name: '$(Date:yyyyMMdd).$(Rev:r)'

trigger: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: Axon.HAComms.ScheduledJobs.AuditCleanupService.Build
      trigger:
        branches:
          include:
          - develop
          - main
  repositories:
    - repository: devops-templates
      name: DevOps/DevOps.Pipelines.Templates
      type: git

parameters:
  - name: environments
    type: object
    default:
      - name: axon_hac_dev_audit_cleanup
        environment: axon_hacomms_dev_audit_cleanup
        cluster: axn-nonprod-aks-eun
        rg: rg-axn-nonprod-eun
        subscription: "Axon Development"
        namespace: axon-hacomms-dev
        valuesOverrideFiles: ["./AuditCleanup/deploy/helm/audit-cleanup-chart/environments/development.values.yaml"]
        keyVaults:
         - subscription: "Axon_Development"
           name: hac-dev-kv-eun
           secretsFilter: 'ConnectionStrings--default,ConnectionStrings--SendGrid'
        dependsOn: []

      - name: axon_hac_int_audit_cleanup
        environment: axon_hacomms_int_audit_cleanup
        cluster: axn-nonprod-aks-eun
        rg: rg-axn-nonprod-eun
        subscription: "Axon Development"
        namespace: axon-hacomms-int
        valuesOverrideFiles: ["./AuditCleanup/deploy/helm/audit-cleanup-chart/environments/integration.values.yaml"]
        keyVaults:
         - subscription: "Axon_Development"
           name: hac-int-kv-eun
           secretsFilter: 'ConnectionStrings--default,ConnectionStrings--SendGrid'
        dependsOn: ["axon_hac_dev_audit_cleanup"]

      - name: axon_hac_stg_audit_cleanup
        environment: axon_hacomms_stg_audit_cleanup
        cluster: axn-stg-aks-eun
        rg: rg-axn-stg-eun
        subscription: "Axon Development"
        namespace: axon-hacomms-stg
        valuesOverrideFiles: ["./AuditCleanup/deploy/helm/audit-cleanup-chart/environments/staging.values.yaml"]
        keyVaults:
         - subscription: "Axon_Development"
           name: hac-stg-kv-eun
           secretsFilter: 'ConnectionStrings--default,ConnectionStrings--SendGrid'
        dependsOn: ["axon_hac_int_audit_cleanup"]

      - name: axon_hac_prodeu_audit_cleanup
        environment: axon_hacomms_prodeu_audit_cleanup
        cluster: axn-prod-aks-eun
        rg: rg-axn-prod-eun
        subscription: "Axon Production"
        namespace: axon-hacomms-prod
        valuesOverrideFiles: ["./AuditCleanup/deploy/helm/audit-cleanup-chart/environments/production-eu.values.yaml"]
        keyVaults:
         - subscription: "Axon_Production"
           name: hac-prod-kv-eun
           secretsFilter: 'ConnectionStrings--default,ConnectionStrings--SendGrid'
        dependsOn: ["axon_hac_stg_audit_cleanup"]

      - name: axon_hac_uat_audit_cleanup
        environment: axon_hacomms_uat_audit_cleanup
        cluster: axn-prod-aks-eun
        rg: rg-axn-prod-eun
        subscription: "Axon Production"
        namespace: axon-hacomms-uat
        valuesOverrideFiles: ["./AuditCleanup/deploy/helm/audit-cleanup-chart/environments/uat.values.yaml"]
        keyVaults:
         - subscription: "Axon_Production"
           name: hac-prod-kv-eun
           secretsFilter: 'ConnectionStrings--default,ConnectionStrings--SendGrid'
        dependsOn: ["axon_hac_stg_audit_cleanup"]

stages:
  - stage: Deploy    
    jobs:
    - job: Initialize
      steps:
      - bash: |          
            echo "Updating build number with pipeline run name"
            echo "##vso[build.updatebuildnumber]$(Build.BuildNumber) Build #$(resources.pipeline.build-pipeline.runName)"         
      displayName: 'Update Pipeline Name'
  - ${{ each env in parameters.environments }}:
    - template: Helm/deploy-single-oidc.yaml@devops-templates
      parameters:
        Environment: ${{ env.environment }}
        cluster: ${{ env.cluster }}
        resourceGroup: ${{ env.rg }}
        subscription: ${{ env.subscription }}
        ChartName: AuditCleanup/deploy/helm/audit-cleanup-chart
        ReleaseName: axon-hacomms-audit-cleanup
        Namespace: ${{ env.namespace }}
        usehelmrepo: false
        valuesOverrideFiles: ${{ env.valuesOverrideFiles }}
        additionalHelmArgs: "--set image.tag=$(resources.pipeline.build-pipeline.runName) --set cronjob.image.tag=$(resources.pipeline.build-pipeline.runName)"
        replaceSecrets: true
        keyVaults: ${{ env.keyVaults }}
        helmTest: false
        pool: "pv-pool"
