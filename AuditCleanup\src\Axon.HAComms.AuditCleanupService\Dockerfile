ARG versionNo=0.0.0.0
ARG nugetSource=https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/nuget/v3/index.json
ARG nugetPassword
ARG projectName=Axon.HAComms
ARG configuration=Release

FROM mcr.microsoft.com/dotnet/aspnet:8.0-noble-chiseled-extra AS base
ARG versionNo
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS publish
ARG nugetSource
ARG nugetPassword
ARG projectName
ARG versionNo
ARG configuration
WORKDIR /
COPY . .

RUN dotnet nuget add source ${nugetSource} -n Phlexglobal -u ignore -p ${nugetPassword} --store-password-in-clear-text \
 && dotnet restore AuditCleanup/test/${projectName}.AuditCleanupService.Tests/${projectName}.AuditCleanupService.Tests.csproj \
 && dotnet restore AuditCleanup/src/${projectName}.AuditCleanupService/${projectName}.AuditCleanupService.csproj --runtime linux-musl-x64 \
 && dotnet publish AuditCleanup/src/${projectName}.AuditCleanupService/${projectName}.AuditCleanupService.csproj \
    -c ${configuration} \
    /property:Version=${versionNo} \
    -o /app/publish \
    --runtime linux-musl-x64 \
 && dotnet nuget remove source Phlexglobal

FROM publish AS testrunner
WORKDIR /
ARG projectName
ARG configuration
ARG waitScript='https://pxgstaticstoprodneu.blob.core.windows.net/scripts/wait'
ENV configuration_env=${configuration}
ENV testProjectName=AuditCleanup/test/${projectName}.AuditCleanupService.Tests/${projectName}.AuditCleanupService.Tests.csproj

ADD ${waitScript} /wait
RUN chmod +x /wait

RUN mkdir -p /tests/output/TestResults
ENTRYPOINT ["/bin/sh", "-c", "echo 'Starting audit cleanup service tests...' && echo \"Test project: $testProjectName\" && echo \"Configuration: $configuration_env\" && if [ -f '/wait' ]; then echo 'Waiting for dependencies...' && /wait; fi && exec dotnet test \"$testProjectName\" -c \"$configuration_env\" --logger \"xunit;LogFilePath=/tests/output/TestResults/UnitTestResults.xml\" --collect \"XPlat Code coverage\" --results-directory \"/tests/output/TestResults\""]

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

USER app

# Globalization settings - either use invariant mode or set proper culture
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=0 \
    LC_ALL=en_US.UTF-8 \
    LANG=en_US.UTF-8

ENTRYPOINT ["dotnet", "Axon.HAComms.AuditCleanupService.dll"]
