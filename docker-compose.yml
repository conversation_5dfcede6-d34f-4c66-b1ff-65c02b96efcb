services:
  axon-hacomms-audit-cleanup:
    image: axon-hacomms-audit-cleanup
    build:
      context: .
      dockerfile: AuditCleanup/src/Axon.HAComms.AuditCleanupService/Dockerfile
      args:
        projectName: Axon.HAComms
        versionNo: "1.0.0"
        configuration: Release
        nugetSource: https://pkgs.dev.azure.com/Phlexglobal/_packaging/Phlexglobal/nuget/v3/index.json
        nugetPassword: ${NUGET_PASSWORD}
    environment:
      - ConnectionStrings__default=Server=host.docker.internal;Initial Catalog=hacomms-dev;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Connection Timeout=30;Integrated Security=True
      - AuditCleanup__RetentionMonths=12
      - AuditCleanup__EmailReceivers=<EMAIL>
