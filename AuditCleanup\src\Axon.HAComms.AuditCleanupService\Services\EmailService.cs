﻿using Axon.HAComms.AuditCleanupService.Interfaces;
using SendGrid.Helpers.Mail;
using SendGrid;
using System.Net;

namespace Axon.HAComms.AuditCleanupService.Services;

public class EmailService : IEmailService
{
    private const string SenderEmail = "<EMAIL>";
    private readonly ISendGridClient sendGridClient;

    public EmailService(ISendGridClient sendGridClient)
    {
        this.sendGridClient = sendGridClient;
    }

    public async Task<(bool, HttpStatusCode)> Send(string[] receivers, string subject, string messageContent)
    {
        SendGridMessage msg = new();
        msg.SetFrom(new EmailAddress(SenderEmail));
        foreach (var receiver in receivers)
        {
            msg.AddTo(receiver);
        }
        msg.SetSubject(subject);
        msg.PlainTextContent = messageContent;

        Response response = await sendGridClient.SendEmailAsync(msg).ConfigureAwait(false);
        return (response.IsSuccessStatusCode, response.StatusCode);
    }
}
