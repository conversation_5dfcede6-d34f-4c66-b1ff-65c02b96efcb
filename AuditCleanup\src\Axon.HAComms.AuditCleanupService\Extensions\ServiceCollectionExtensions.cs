using Axon.HAComms.AuditCleanupService.Interfaces;
using Axon.HAComms.AuditCleanupService.Repositories;
using Axon.HAComms.AuditCleanupService.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SendGrid;
using SendGrid.Extensions.DependencyInjection;

namespace Axon.HAComms.AuditCleanupService.Extensions;

public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds SendGrid services to the service collection
    /// </summary>
    public static IServiceCollection AddSendGridServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Add SendGrid
        services.AddSendGrid(options =>
        {
            var apiKey = configuration["ConnectionStrings:SendGrid"];

            if (string.IsNullOrEmpty(apiKey))
            {
                throw new InvalidOperationException(
                    "SendGrid API key not found. Please ensure 'SendGrid' is available in Key Vault, " +
                    "or set 'ConnectionStrings:SendGrid' for local development.");
            }

            options.ApiKey = apiKey;
        });

        // Add email service
        services.AddTransient<IEmailService>(provider =>
        {
            var sendGridClient = provider.GetRequiredService<ISendGridClient>();
            return new EmailService(sendGridClient);
        });

        return services;
    }

    /// <summary>
    /// Adds audit cleanup services to the service collection
    /// </summary>
    public static IServiceCollection AddAuditCleanupServices(this IServiceCollection services)
    {
        services.AddScoped<IAuditRepository, AuditRepository>();
        services.AddScoped<IAuditCleanupService, Services.AuditCleanupService>();
        services.AddScoped<IEmailNotificationService, EmailNotificationService>();

        return services;
    }
}
