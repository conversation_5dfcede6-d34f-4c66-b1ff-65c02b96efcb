namespace Axon.HAComms.AuditCleanupService.Models;

public class AuditCleanupResult
{
    public required bool Success { get; init; }
    public required int RecordsDeleted { get; init; }
    public required DateTime ExecutionTime { get; init; }
    public required TimeSpan Duration { get; init; }
    public string? ErrorMessage { get; init; }
    public Exception? Exception { get; init; }
    public required int RetentionMonths { get; init; }
    public required DateTime ThresholdDate { get; init; }
}
