{{- if .Values.cronjob.enabled -}}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "hacomms-audit-cleanup.fullname" . }}-cronjob
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "hacomms-audit-cleanup.labels" . | nindent 4 }}
  annotations:
    checkov.io/skip1: CKV_K8S_14=The tag is being defined as helm argument
    checkov.io/skip2: CKV_K8S_43=We cannot use digest since these are regenerated on every image
    checkov.io/skip3: CKV_K8S_38=AutomountServiceAccountToken is required for Key Vault access
spec:
  schedule: "{{ .Values.cronjob.schedule }}"
  {{- with .Values.cronjob.concurrencyPolicy }}
  concurrencyPolicy: {{ . }}
  {{- end }}
  {{- with .Values.cronjob.suspend }}
  suspend: {{ . }}
  {{- end }}
  failedJobsHistoryLimit: {{ .Values.cronjob.failedJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ .Values.cronjob.successfulJobsHistoryLimit }}
  jobTemplate:
    metadata:
      labels:
        {{- include "hacomms-audit-cleanup.selectorLabels" . | nindent 8 }}
        azure.workload.identity/use: "true"
    spec:
      {{- with .Values.cronjob.backoffLimit }}
      backoffLimit: {{ . }}
      {{- end }}
      {{- with .Values.cronjob.activeDeadlineSeconds }}
      activeDeadlineSeconds: {{ . }}
      {{- end }}
      template:
        metadata:
          labels:
            {{- include "hacomms-audit-cleanup.selectorLabels" . | nindent 12 }}
            azure.workload.identity/use: "true"
          {{- with .Values.pod.annotations }}
          annotations:
{{ toYaml . | indent 12 }}
          {{- end }}
        spec:
          automountServiceAccountToken: true
          restartPolicy: OnFailure
          securityContext:
            {{- toYaml .Values.podSecurityContext | nindent 12 }}
          serviceAccountName: {{ .Values.AzureAd.AppName }}
          containers:
            - name: {{ .Chart.Name }}
              image: "{{ .Values.cronjob.image.repository }}:{{ .Values.cronjob.image.tag }}"
              securityContext:
                {{- toYaml .Values.securityContext | nindent 16 }}
              imagePullPolicy: {{ .Values.cronjob.image.pullPolicy }}
              env:
                # Database connection
                - name: ConnectionStrings__default
                  valueFrom:
                    secretKeyRef:
                      name: {{ .Values.database.secretName }}
                      key: {{ .Values.database.secretKey }}

                # SendGrid API key (from Key Vault via secret)
                - name: ConnectionStrings__SendGrid
                  valueFrom:
                    secretKeyRef:
                      name: {{ .Values.sendgrid.secretName | default .Values.database.secretName }}
                      key: {{ .Values.sendgrid.secretKey | default "SendGrid" }}
                
                # Audit cleanup configuration
                - name: AuditCleanup__RetentionMonths
                  value: "{{ .Values.app.retentionMonths }}"
                
                - name: AuditCleanup__EmailReceivers
                  value: "{{ .Values.app.emailReceivers }}"

                # Azure Key Vault configuration
                - name: KeyVaultName
                  value: "{{ .Values.keyVaultName }}"

                # Azure AD configuration
                - name: AzureAd__Instance
                  value: "{{ .Values.AzureAd.Instance }}"

                - name: AzureAd__TenantId
                  value: "{{ .Values.AzureAd.TenantId }}"

                - name: AzureAd__ClientId
                  value: "{{ .Values.AzureAd.ClientId }}"

                - name: AzureIdentity__ManageIdentityClientId
                  value: {{ .Values.AzureAd.ClientId }}

                - name: AzureAd__Audience
                  value: "{{ .Values.AzureAd.Audience }}"

                - name: AzureAd__AllowWebApiToBeAuthorizedByACL
                  value: "{{ .Values.AzureAd.AllowWebApiToBeAuthorizedByACL }}"

                - name: AzureAd__Scope
                  value: "{{ .Values.AzureAd.Scope }}"

                # Environment configuration
                - name: ASPNETCORE_ENVIRONMENT
                  value: "{{ .Values.aspNetCoreEnvironment }}"

                # Logging configuration
                - name: Logging__LogLevel__Default
                  value: "{{ .Values.app.logLevel }}"
              
              resources:
{{ toYaml .Values.cronjob.resources | indent 16 }}
              
              volumeMounts:
                - name: tmp
                  mountPath: /tmp
                - name: app-tmp
                  mountPath: /app/tmp
          
          {{- with .Values.nodeSelector }}
          nodeSelector:
{{ toYaml . | indent 12 }}
          {{- end }}
          
          volumes:
            - name: tmp
              emptyDir: {}
            - name: app-tmp
              emptyDir: {}
          
          {{- with .Values.affinity }}
          affinity:
{{ toYaml . | indent 12 }}
          {{- end }}
          
          {{- with .Values.tolerations }}
          tolerations:
{{ toYaml . | indent 12 }}
          {{- end }}
{{- end }}
