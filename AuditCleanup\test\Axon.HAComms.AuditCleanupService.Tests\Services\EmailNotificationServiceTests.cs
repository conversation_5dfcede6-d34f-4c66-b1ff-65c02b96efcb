using Axon.HAComms.AuditCleanupService.Interfaces;
using Axon.HAComms.AuditCleanupService.Models;
using Axon.HAComms.AuditCleanupService.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using System.Net;
using Xunit;

namespace Axon.HAComms.AuditCleanupService.Tests.Services;

public class EmailNotificationServiceTests
{
    private readonly EmailNotificationService _sut;
    private readonly IEmailService _mockEmailService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<EmailNotificationService> _mockLogger;

    public EmailNotificationServiceTests()
    {
        _mockEmailService = Substitute.For<IEmailService>();
        _mockLogger = Substitute.For<ILogger<EmailNotificationService>>();
        
        var configData = new Dictionary<string, string?>
        {
            {"AuditCleanup:EmailReceivers", "<EMAIL>;<EMAIL>"}
        };

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();

        _sut = new EmailNotificationService(_mockEmailService, _configuration, _mockLogger);
    }

    [Fact]
    public async Task SendCleanupNotificationAsync_SuccessResult_SendsSuccessEmail()
    {
        // Arrange
        var result = new AuditCleanupResult
        {
            Success = true,
            RecordsDeleted = 100,
            ExecutionTime = DateTime.UtcNow,
            Duration = TimeSpan.FromMinutes(2),
            RetentionMonths = 12,
            ThresholdDate = DateTime.UtcNow.AddMonths(-12)
        };

        _mockEmailService.Send(Arg.Any<string[]>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns((true, HttpStatusCode.OK));

        // Act
        await _sut.SendCleanupNotificationAsync(result);

        // Assert
        await _mockEmailService.Received(1).Send(
            Arg.Is<string[]>(recipients => recipients.Length == 2 && 
                           recipients.Contains("<EMAIL>") && 
                           recipients.Contains("<EMAIL>")),
            "HAComms Audit Cleanup - Success",
            Arg.Is<string>(body => body.Contains("SUCCESS") && body.Contains("100")));
    }

    [Fact]
    public async Task SendCleanupNotificationAsync_FailureResult_SendsFailureEmail()
    {
        // Arrange
        var result = new AuditCleanupResult
        {
            Success = false,
            RecordsDeleted = 0,
            ExecutionTime = DateTime.UtcNow,
            Duration = TimeSpan.FromMinutes(1),
            RetentionMonths = 12,
            ThresholdDate = DateTime.UtcNow.AddMonths(-12),
            ErrorMessage = "Database connection failed"
        };

        _mockEmailService.Send(Arg.Any<string[]>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns((true, HttpStatusCode.OK));

        // Act
        await _sut.SendCleanupNotificationAsync(result);

        // Assert
        await _mockEmailService.Received(1).Send(
            Arg.Any<string[]>(),
            "HAComms Audit Cleanup - Failed",
            Arg.Is<string>(body => body.Contains("FAILED") && body.Contains("Database connection failed")));
    }

    [Fact]
    public async Task SendCleanupNotificationAsync_NoEmailRecipients_DoesNotSendEmail()
    {
        // Arrange
        var configData = new Dictionary<string, string?>(); // No email recipients
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();

        var sut = new EmailNotificationService(_mockEmailService, configuration, _mockLogger);
        
        var result = new AuditCleanupResult
        {
            Success = true,
            RecordsDeleted = 50,
            ExecutionTime = DateTime.UtcNow,
            Duration = TimeSpan.FromMinutes(1),
            RetentionMonths = 12,
            ThresholdDate = DateTime.UtcNow.AddMonths(-12)
        };

        // Act
        await sut.SendCleanupNotificationAsync(result);

        // Assert
        await _mockEmailService.DidNotReceive().Send(Arg.Any<string[]>(), Arg.Any<string>(), Arg.Any<string>());
    }

    [Fact]
    public async Task SendCleanupNotificationAsync_EmailServiceThrows_LogsError()
    {
        // Arrange
        var result = new AuditCleanupResult
        {
            Success = true,
            RecordsDeleted = 25,
            ExecutionTime = DateTime.UtcNow,
            Duration = TimeSpan.FromMinutes(1),
            RetentionMonths = 12,
            ThresholdDate = DateTime.UtcNow.AddMonths(-12)
        };

        _mockEmailService.When(x => x.Send(Arg.Any<string[]>(), Arg.Any<string>(), Arg.Any<string>()))
            .Do(x => throw new InvalidOperationException("Email service unavailable"));

        // Act & Assert
        await Should.NotThrowAsync(async () => await _sut.SendCleanupNotificationAsync(result));
    }
}
