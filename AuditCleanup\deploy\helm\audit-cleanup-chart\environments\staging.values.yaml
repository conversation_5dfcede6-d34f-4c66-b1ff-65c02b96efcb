image:
  repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
  tag: latest
  pullPolicy: Always

global:
  namespace: axon-hacomms-stg

cronjob:
  enabled: true
  schedule: "0 2 1 * *"
  image:
    repository: phlexglobal.azurecr.io/axon-hacomms-audit-cleanup
    tag: stg
    pullPolicy: Always

  resources:
    requests:
      memory: "128Mi"
      cpu: "50m"
    limits:
      memory: "256Mi"
      cpu: "200m"

  rbac:
    enabled: true

app:
  retentionMonths: 3
  emailReceivers: "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>"
  environment: Staging
  logLevel: Information

keyVaultName: hac-stg-kv-eun
aspNetCoreEnvironment: Staging

databaseConnectionString: "#{database-connection-string-stg}#"
sendGridApiKey: "#{sendgrid-api-key-stg}#"

AzureAd:
  AppName: axon-hacomms-stg
  Instance: "https://login.microsoftonline.com/"
  TenantId: "66b904a2-2bfc-4d24-a410-96b77b32bf77"
  ClientId: "30743dd3-bf91-436a-a9be-2ec2010e0e78"
  Audience: "167cd45b-7d4f-4b3d-8c05-a87f12c40609"
  TokenExpiration: '86400' # Token is valid for 1 day
  AllowWebApiToBeAuthorizedByACL: true
  Scope: "api://smartphlex-stg/.default"

database:
  secretName: hacomms-stg-secrets
  secretKey: ConnectionStrings--default

sendgrid:
  secretName: sendgrid-stg-secret
  secretKey: ConnectionStrings--SendGrid

secrets:
  enabled: true
