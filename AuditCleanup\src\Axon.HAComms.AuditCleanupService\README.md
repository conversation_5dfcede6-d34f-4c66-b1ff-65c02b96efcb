# Audit Cleanup Service

A lightweight console application that performs automated cleanup of old audit data from the database using direct ADO.NET connectivity for optimal performance.

## What it does

1. Connects to SQL Server database using ADO.NET
2. Deletes audit records older than the configured retention period (default: 3 months)
3. Sends email notification with cleanup results via SendGrid
4. Logs all operations and exits with appropriate status code

## Dependencies

- **Microsoft.Data.SqlClient**: Direct SQL Server connectivity (replaced Entity Framework)
- **SendGrid**: Email notifications
- **Azure.Security.KeyVault.Secrets**: Secure configuration management
- **Microsoft.Extensions.Hosting**: Console application hosting
- **Microsoft.Extensions.Logging**: Comprehensive logging

## Setup

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `ConnectionStrings__default` | SQL Server connection string | - | Yes |
| `KeyVaultName` | Key Vault name (without .vault.azure.net) | - | No* |
| `SendGrid` | SendGrid API key for email notifications | - | Yes |
| `AzureAd__TenantId` | Azure AD tenant ID | - | No* |
| `AzureAd__ClientId` | Azure AD client ID | - | No* |
| `AuditCleanup__EmailReceivers` | Email addresses (separated by ;) | - | Yes |
| `AuditCleanup__RetentionMonths` | Months to retain audit data | 3 | No |

*Required for production deployments using Azure Key Vault

## Run with Docker

```bash
docker run --rm \
  -e ConnectionStrings__default="your-connection-string" \
  -e KeyVaultName="your-keyvault-name" \
  -e SendGrid="your-sendgrid-api-key" \
  -e AuditCleanup__EmailReceivers="<EMAIL>" \
  -e AzureAd__TenantId="your-tenant-id" \
  -e AzureAd__ClientId="your-client-id" \
  hacomms-audit-cleanup
```

## Helm Deployment

### Development (with built-in secrets)
```bash
# Development deployment - creates secrets automatically
helm install audit-cleanup ./deploy/helm/audit-cleanup-chart \
  -f ./deploy/helm/audit-cleanup-chart/environments/development.values.yaml \
  --namespace axon-hacomms-dev
```
```

## Build

```bash
# Build project
dotnet build src/Axon.HAComms.AuditCleanupService/

# Build Docker image
docker build -f src/Axon.HAComms.AuditCleanupService/Dockerfile -t hacomms-audit-cleanup .
```

## Troubleshooting

- **Key Vault errors**: Check Azure AD application permissions to Key Vault
- **Database errors**: Verify connection string in Kubernetes secret
- **SendGrid Unauthorized (401)**: Check SendGrid API key in Key Vault secret `SendGrid`
- **No emails sent**: Verify email configuration and SendGrid API key validity
- **Authentication errors**: Ensure Azure AD configuration is correct
