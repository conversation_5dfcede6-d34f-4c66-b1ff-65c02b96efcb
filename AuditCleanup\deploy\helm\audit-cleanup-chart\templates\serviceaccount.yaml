{{- if .Values.svcAccount.enabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.AzureAd.AppName }}
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "hacomms-audit-cleanup.labels" . | nindent 4 }}
    azure.workload.identity/use: "true"
  annotations:
    azure.workload.identity/client-id: {{ .Values.AzureAd.ClientId }}
    azure.workload.identity/tenant-id: {{ .Values.AzureAd.TenantId }}
    azure.workload.identity/service-account-token-expiration: {{ quote .Values.AzureAd.TokenExpiration }}
automountServiceAccountToken: false
{{- end }}
